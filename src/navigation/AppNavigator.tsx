import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import Icon from 'react-native-vector-icons/MaterialIcons';

// 全局导航类型声明
declare global {
  namespace ReactNavigation {
    interface RootParamList extends RootStackParamList {}
  }
}

// 导入页面
import ProfileScreen from '../screens/ProfileScreen';
import EditScreen from '../screens/EditScreen';
import RecordScreen from '../screens/RecordScreen';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';

// 定义底部标签导航类型
export type TabParamList = {
  Edit: undefined;
  Record: undefined;
  Profile: undefined;
};

// 定义主导航栈类型
export type RootStackParamList = {
  MainTabs: undefined;
  Detail: undefined;
};

// 创建Stack导航栈
const Stack = createNativeStackNavigator<RootStackParamList>();
// 创建BottomTab导航栈
const Tab = createBottomTabNavigator<TabParamList>();

// 定义底部标签页的导航器
function HomeTabs() {
  return (
    <Tab.Navigator
      screenOptions={{
        // 可以在这里统一设置所有 Tab 的样式
        headerShown: false, // 如果你希望每个 Tab 内部有独立的 header，可以在这里关闭
        tabBarActiveTintColor: '#1a73e8', // 选中标签的文字颜色
        tabBarInactiveTintColor: 'gray',  // 未选中标签的文字颜色
        tabBarStyle: {
          backgroundColor: '#fff', // 标签栏背景色
          borderTopWidth: 1,
          borderTopColor: '#e0e0e0',
        },
        tabBarItemStyle: {
          paddingVertical: 6,
        },
      }}
    >
      <Tab.Screen
        name="Edit"
        component={EditScreen}
        options={{
          title: '转换编辑',
          tabBarIcon: ({ color, size }) => (
            <Icon name="edit" color={color} size={size} />
          ),
        }}
      />
      <Tab.Screen
        name="Record"
        component={RecordScreen}
        options={{
          title: '文件记录',
          tabBarIcon: ({ color, size }) => (
            <Icon name="history" color={color} size={size} />
          ),
        }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileScreen}
        options={{
          title: '个人中心',
          tabBarIcon: ({ color, size }) => (
            <Icon name="person" color={color} size={size} />
          ),
        }}
      />
    </Tab.Navigator>
  );
}


const AppNavigator = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName="MainTabs"
        screenOptions={{
          headerStyle: {
            backgroundColor: '#1a73e8',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      >
        <Stack.Screen 
          name="MainTabs" 
          component={HomeTabs} 
          options={{ headerShown: false }} 
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;